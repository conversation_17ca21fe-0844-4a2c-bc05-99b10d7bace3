import { Component, OnIni<PERSON>, <PERSON><PERSON><PERSON><PERSON>, AfterViewInit } from '@angular/core';

// Declare Chart.js types for TypeScript
declare var Chart: any;

interface TemperatureData {
  DateTime: string;
  LabName: string;
  Temperature: number;
}

interface LabData {
  data: TemperatureData[];
  chart: any;
  lastUpdate: Date;
}

interface LabStatistics {
  labName: string;
  maxTemp: number;
  minTemp: number;
  avgTemp: number;
  currentTemp: number;
  maxTempTime: Date | null;
  minTempTime: Date | null;
  currentTempTime: Date | null;
  dataPointsCount: number;
  hasRecentData: boolean;
}

@Component({
  selector: 'app-root',
  templateUrl: './app.component.html',
  styleUrls: ['./app.component.css']
})
export class AppComponent implements OnInit, OnDestroy, AfterViewInit {
  title = 'Real-time Temperature Monitoring';
  private socket: WebSocket | undefined;
  public isConnected = false;

  // Store data and charts for each lab
  private labsData: Map<string, LabData> = new Map();

  // Store all historical data for analytics (not limited like chart data)
  private allLabsData: Map<string, TemperatureData[]> = new Map();

  // Maximum number of data points to display per chart
  private readonly MAX_DATA_POINTS = 50;

  // Analytics constants
  private readonly ANALYTICS_DAYS = 5;
  private readonly HOURS_IN_DAY = 24;
  private readonly ANALYTICS_HOURS = this.ANALYTICS_DAYS * this.HOURS_IN_DAY;

  ngOnInit() {
    this.initializeWebSocket();
  }

  ngAfterViewInit() {
    // Charts will be created dynamically when data arrives
  }

  ngOnDestroy() {
    if (this.socket) {
      this.socket.close();
    }

    // Destroy all charts
    this.labsData.forEach(labData => {
      if (labData.chart) {
        labData.chart.destroy();
      }
    });
  }

  private initializeWebSocket() {
    this.socket = new WebSocket("ws://localhost:8765");

    this.socket.onopen = () => {
      console.log("Connected to WebSocket server.");
      this.isConnected = true;
    };

    this.socket.onmessage = (event) => {
      try {
        const data: TemperatureData[] = JSON.parse(event.data);
        console.log("Temperature data received:", data);
        this.processIncomingData(data);
      } catch (error) {
        console.error("Error parsing WebSocket data:", error);
      }
    };

    this.socket.onclose = () => {
      console.log("WebSocket connection closed.");
      this.isConnected = false;

      // Attempt to reconnect after 3 seconds
      setTimeout(() => {
        if (!this.isConnected) {
          console.log("Attempting to reconnect...");
          this.initializeWebSocket();
        }
      }, 3000);
    };

    this.socket.onerror = (error) => {
      console.error("WebSocket error:", error);
      this.isConnected = false;
    };
  }

  private processIncomingData(data: TemperatureData[]) {
    const dataArray = Array.isArray(data) ? data : [data];
    dataArray.forEach(dataPoint => {
      const labName = dataPoint.LabName;

      // Initialize lab data if it doesn't exist
      if (!this.labsData.has(labName)) {
        this.labsData.set(labName, {
          data: [],
          chart: null,
          lastUpdate: new Date()
        });
      }

      // Initialize analytics data if it doesn't exist
      if (!this.allLabsData.has(labName)) {
        this.allLabsData.set(labName, []);
      }

      const labData = this.labsData.get(labName)!;
      const allData = this.allLabsData.get(labName)!;

      // Add new data point to both collections
      labData.data.push(dataPoint);
      allData.push(dataPoint);
      labData.lastUpdate = new Date();

      // Keep only the last MAX_DATA_POINTS for charts
      if (labData.data.length > this.MAX_DATA_POINTS) {
        labData.data = labData.data.slice(-this.MAX_DATA_POINTS);
      }

      // Clean old data from analytics collection (keep only last 5 days)
      this.cleanOldAnalyticsData(labName);

      // Create or update chart
      this.updateChart(labName);
    });
  }

  private updateChart(labName: string) {
    const labData = this.labsData.get(labName);
    if (!labData) return;

    // Wait for the DOM element to be available
    setTimeout(() => {
      const canvasElement = document.getElementById(`chart-${labName}`) as HTMLCanvasElement;
      if (!canvasElement) {
        console.warn(`Canvas element for ${labName} not found`);
        return;
      }

      // Destroy existing chart if it exists
      if (labData.chart) {
        labData.chart.destroy();
      }

      // Prepare data for Chart.js
      const labels = labData.data.map(d => new Date(d.DateTime).toLocaleTimeString());
      const temperatures = labData.data.map(d => d.Temperature);

      // Create new chart
      labData.chart = new Chart(canvasElement, {
        type: 'line',
        data: {
          labels: labels,
          datasets: [{
            label: `${labName} Temperature (°C)`,
            data: temperatures,
            borderColor: this.getColorForLab(labName),
            backgroundColor: this.getColorForLab(labName, 0.1),
            borderWidth: 2,
            fill: true,
            tension: 0.4,
            pointRadius: 3,
            pointHoverRadius: 5
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          scales: {
            x: {
              title: {
                display: true,
                text: 'Time'
              },
              ticks: {
                maxTicksLimit: 10
              }
            },
            y: {
              title: {
                display: true,
                text: 'Temperature (°C)'
              },
              beginAtZero: false
            }
          },
          plugins: {
            legend: {
              display: true,
              position: 'top'
            },
            tooltip: {
              mode: 'index',
              intersect: false,
              callbacks: {
                label: function(context: any) {
                  return `${context.dataset.label}: ${context.parsed.y.toFixed(2)}°C`;
                }
              }
            }
          },
          interaction: {
            mode: 'nearest',
            axis: 'x',
            intersect: false
          },
          animation: {
            duration: 750
          }
        }
      });
    }, 100);
  }

  private getColorForLab(labName: string, alpha: number = 1): string {
    // Generate consistent colors for each lab
    const colors = [
      `rgba(255, 99, 132, ${alpha})`,   // Red
      `rgba(54, 162, 235, ${alpha})`,   // Blue
      `rgba(255, 205, 86, ${alpha})`,   // Yellow
      `rgba(75, 192, 192, ${alpha})`,   // Green
      `rgba(153, 102, 255, ${alpha})`,  // Purple
      `rgba(255, 159, 64, ${alpha})`,   // Orange
      `rgba(199, 199, 199, ${alpha})`,  // Grey
      `rgba(83, 102, 255, ${alpha})`,   // Indigo
    ];

    // Use a simple hash function to consistently assign colors
    let hash = 0;
    for (let i = 0; i < labName.length; i++) {
      hash = labName.charCodeAt(i) + ((hash << 5) - hash);
    }
    const colorIndex = Math.abs(hash) % colors.length;
    return colors[colorIndex];
  }

  // Analytics methods
  private cleanOldAnalyticsData(labName: string) {
    const allData = this.allLabsData.get(labName);
    if (!allData) return;

    const cutoffTime = new Date();
    cutoffTime.setHours(cutoffTime.getHours() - this.ANALYTICS_HOURS);

    // Filter out data older than 5 days
    const recentData = allData.filter(dataPoint => {
      const dataTime = new Date(dataPoint.DateTime);
      return dataTime >= cutoffTime;
    });

    this.allLabsData.set(labName, recentData);
  }

  private getRecentData(labName: string): TemperatureData[] {
    const allData = this.allLabsData.get(labName);
    if (!allData) return [];

    const cutoffTime = new Date();
    cutoffTime.setHours(cutoffTime.getHours() - this.ANALYTICS_HOURS);

    return allData.filter(dataPoint => {
      const dataTime = new Date(dataPoint.DateTime);
      return dataTime >= cutoffTime;
    });
  }

  public getLabStatistics(labName: string): LabStatistics {
    const recentData = this.getRecentData(labName);

    if (recentData.length === 0) {
      return {
        labName,
        maxTemp: 0,
        minTemp: 0,
        avgTemp: 0,
        currentTemp: 0,
        maxTempTime: null,
        minTempTime: null,
        currentTempTime: null,
        dataPointsCount: 0,
        hasRecentData: false
      };
    }

    // Sort by date to find current (most recent) temperature
    const sortedData = recentData.sort((a, b) =>
      new Date(b.DateTime).getTime() - new Date(a.DateTime).getTime()
    );

    const temperatures = recentData.map(d => d.Temperature);
    const maxTemp = Math.max(...temperatures);
    const minTemp = Math.min(...temperatures);
    const avgTemp = temperatures.reduce((sum, temp) => sum + temp, 0) / temperatures.length;

    // Find the data points with max and min temperatures
    const maxTempData = recentData.find(d => d.Temperature === maxTemp);
    const minTempData = recentData.find(d => d.Temperature === minTemp);
    const currentTempData = sortedData[0];

    return {
      labName,
      maxTemp: parseFloat(maxTemp.toFixed(2)),
      minTemp: parseFloat(minTemp.toFixed(2)),
      avgTemp: parseFloat(avgTemp.toFixed(2)),
      currentTemp: parseFloat(currentTempData.Temperature.toFixed(2)),
      maxTempTime: maxTempData ? new Date(maxTempData.DateTime) : null,
      minTempTime: minTempData ? new Date(minTempData.DateTime) : null,
      currentTempTime: new Date(currentTempData.DateTime),
      dataPointsCount: recentData.length,
      hasRecentData: true
    };
  }

  public getAllLabStatistics(): LabStatistics[] {
    return this.getLabNames().map(labName => this.getLabStatistics(labName));
  }

  // Template methods
  public getLabNames(): string[] {
    return Array.from(this.labsData.keys()).sort();
  }

  public getDataCount(labName: string): number {
    const labData = this.labsData.get(labName);
    return labData ? labData.data.length : 0;
  }

  public getLastUpdate(labName: string): Date | null {
    const labData = this.labsData.get(labName);
    return labData ? labData.lastUpdate : null;
  }

  // Utility methods for dashboard
  public getTemperatureColorClass(temperature: number): string {
    if (temperature >= 30) return 'temp-very-hot';
    if (temperature >= 25) return 'temp-hot';
    if (temperature >= 20) return 'temp-normal';
    if (temperature >= 15) return 'temp-cool';
    return 'temp-cold';
  }

  public formatTemperature(temperature: number): string {
    return `${temperature.toFixed(1)}°C`;
  }

  public getTimeSinceUpdate(date: Date | null): string {
    if (!date) return 'No data';

    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMinutes = Math.floor(diffMs / (1000 * 60));
    const diffHours = Math.floor(diffMinutes / 60);
    const diffDays = Math.floor(diffHours / 24);

    if (diffDays > 0) return `${diffDays}d ago`;
    if (diffHours > 0) return `${diffHours}h ago`;
    if (diffMinutes > 0) return `${diffMinutes}m ago`;
    return 'Just now';
  }
}