import { Component, OnInit, On<PERSON><PERSON>roy } from '@angular/core';

@Component({
  selector: 'app-root',
  templateUrl: './app.component.html',
  styleUrls: ['./app.component.css']
})
export class AppComponent implements OnInit, OnDestroy {
  title = 'temp-app';
  private socket: WebSocket | undefined;

  ngOnInit() {
    this.socket = new WebSocket("ws://localhost:8765");

    this.socket.onmessage = (event) => {
      const data = JSON.parse(event.data);
      console.log("CSV data received:", data);
    };

    this.socket.onopen = () => {
      console.log("Connected to WebSocket server.");
    };
  }

  ngOnDestroy() {
    if (this.socket) {
      this.socket.close();
    }
  }
}