# Debug Instructions for Chart Issues

## Current Status
The application has been updated with debugging logs and simplified chart creation. Here's how to debug the chart plotting issue:

## Steps to Debug

### 1. Start the Application
```bash
ng serve --port 4201
```

### 2. Start the WebSocket Server
```bash
node websocket-server.js
```

### 3. Open Browser Console
- Open `http://localhost:4201` in your browser
- Open Developer Tools (F12)
- Go to the Console tab

### 4. Check for Debug Messages
You should see these console messages:

**When Chart.js loads:**
```
Waiting for Chart.js to load...
Chart.js is loaded and ready
```

**When WebSocket connects:**
```
Connected to WebSocket server.
```

**When data is received:**
```
Temperature data received: [...]
Processing incoming data: [...]
Data array: [...]
```

**When charts are created:**
```
Updating chart for Lab1, data points: 1
Canvas element found for Lab1
Chart data for Lab1: {labels: [...], temperatures: [...]}
Chart created successfully for Lab1
```

## Common Issues and Solutions

### Issue 1: Chart.js Not Loading
**Symptoms:** Console shows "Chart.js is not loaded, retrying in 1 second..."
**Solution:** Check internet connection or try loading Chart.js locally

### Issue 2: Canvas Elements Not Found
**Symptoms:** Console shows "Canvas element for LabX not found"
**Solution:** The Angular template might not be rendering properly

### Issue 3: WebSocket Connection Issues
**Symptoms:** Connection status shows "Disconnected"
**Solution:** Make sure the WebSocket server is running on port 8765

### Issue 4: No Data Received
**Symptoms:** No "Processing incoming data" messages
**Solution:** Check WebSocket server is sending data in correct format

## Simplified Test

If charts still don't work, try this minimal test:

1. Open browser console
2. Type: `typeof Chart`
3. Should return "function" if Chart.js is loaded
4. Type: `document.getElementById('chart-Lab1')`
5. Should return the canvas element if template is rendered

## Manual Chart Test

If Chart.js is loaded but charts don't appear, try creating a chart manually in console:

```javascript
const canvas = document.getElementById('chart-Lab1');
if (canvas) {
  new Chart(canvas, {
    type: 'line',
    data: {
      labels: ['1', '2', '3'],
      datasets: [{
        label: 'Test',
        data: [20, 25, 22],
        borderColor: 'rgb(75, 192, 192)'
      }]
    }
  });
}
```

## Next Steps

Based on the console output, we can identify:
1. Is Chart.js loading properly?
2. Is the WebSocket receiving data?
3. Are the canvas elements being created?
4. Is the chart creation code executing?

Please run the application and share the console output so we can pinpoint the exact issue.
