.app-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
  font-family: Arial, sans-serif;
}

.app-title {
  text-align: center;
  color: #333;
  margin-bottom: 30px;
}

.connection-status {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 30px;
  padding: 10px;
  border-radius: 5px;
  background-color: #f5f5f5;
}

.status-indicator {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  margin-right: 8px;
}

.status-indicator.connected {
  background-color: #4CAF50;
}

.status-indicator.disconnected {
  background-color: #f44336;
}

.status-text {
  font-weight: bold;
}

.charts-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(500px, 1fr));
  gap: 30px;
  margin-bottom: 30px;
}

.chart-wrapper {
  border: 1px solid #ddd;
  border-radius: 8px;
  padding: 20px;
  background-color: #fff;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.chart-title {
  margin: 0 0 15px 0;
  color: #333;
  text-align: center;
}

.chart-info {
  display: flex;
  justify-content: space-between;
  margin-bottom: 15px;
  font-size: 12px;
  color: #666;
}

.chart-container {
  position: relative;
  height: 300px;
  width: 100%;
}

.chart-container canvas {
  max-width: 100%;
  max-height: 100%;
}

.no-data {
  text-align: center;
  padding: 50px;
  color: #666;
  font-style: italic;
}

.data-count, .last-update {
  padding: 4px 8px;
  background-color: #f0f0f0;
  border-radius: 4px;
}

/* Analytics Dashboard Styles */
.analytics-dashboard {
  margin-bottom: 40px;
  padding: 20px;
  background-color: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.dashboard-title {
  text-align: center;
  color: #333;
  margin-bottom: 25px;
  font-size: 24px;
  font-weight: 600;
}

.analytics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 20px;
}

.analytics-card {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  border: 1px solid #e9ecef;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.analytics-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 2px solid #f1f3f4;
}

.lab-name {
  margin: 0;
  color: #2c3e50;
  font-size: 20px;
  font-weight: 700;
}

.data-points-badge {
  background-color: #6c757d;
  color: white;
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 500;
}

.stats-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 15px;
}

.stat-item {
  text-align: center;
  padding: 15px;
  border-radius: 8px;
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
}

.stat-label {
  font-size: 12px;
  font-weight: 600;
  color: #6c757d;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: 8px;
}

.stat-value {
  font-size: 24px;
  font-weight: 700;
  margin-bottom: 5px;
}

.stat-time {
  font-size: 11px;
  color: #6c757d;
  font-style: italic;
}

/* Temperature color coding */
.temp-very-hot {
  color: #dc3545;
  background-color: #f8d7da;
}

.temp-hot {
  color: #fd7e14;
  background-color: #ffeaa7;
}

.temp-normal {
  color: #28a745;
  background-color: #d4edda;
}

.temp-cool {
  color: #17a2b8;
  background-color: #d1ecf1;
}

.temp-cold {
  color: #6f42c1;
  background-color: #e2d9f3;
}

/* Special styling for current temperature */
.current-temp {
  grid-column: 1 / -1;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
}

.current-temp .stat-label,
.current-temp .stat-time {
  color: rgba(255, 255, 255, 0.9);
}

.current-temp .stat-value {
  color: white;
  font-size: 28px;
}

.no-recent-data {
  text-align: center;
  padding: 40px 20px;
  color: #6c757d;
  font-style: italic;
}

.no-recent-data p {
  margin: 0;
  font-size: 16px;
}