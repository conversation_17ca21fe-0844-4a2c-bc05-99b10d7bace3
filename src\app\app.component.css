.app-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
  font-family: Arial, sans-serif;
}

.app-title {
  text-align: center;
  color: #333;
  margin-bottom: 30px;
}

.connection-status {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 30px;
  padding: 10px;
  border-radius: 5px;
  background-color: #f5f5f5;
}

.status-indicator {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  margin-right: 8px;
}

.status-indicator.connected {
  background-color: #4CAF50;
}

.status-indicator.disconnected {
  background-color: #f44336;
}

.status-text {
  font-weight: bold;
}

.charts-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(500px, 1fr));
  gap: 30px;
  margin-bottom: 30px;
}

.chart-wrapper {
  border: 1px solid #ddd;
  border-radius: 8px;
  padding: 20px;
  background-color: #fff;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.chart-title {
  margin: 0 0 15px 0;
  color: #333;
  text-align: center;
}

.chart-info {
  display: flex;
  justify-content: space-between;
  margin-bottom: 15px;
  font-size: 12px;
  color: #666;
}

.chart-container {
  position: relative;
  height: 300px;
  width: 100%;
}

.chart-container canvas {
  max-width: 100%;
  max-height: 100%;
}

.no-data {
  text-align: center;
  padding: 50px;
  color: #666;
  font-style: italic;
}

.data-count, .last-update {
  padding: 4px 8px;
  background-color: #f0f0f0;
  border-radius: 4px;
}