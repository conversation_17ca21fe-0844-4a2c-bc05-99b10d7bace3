# Real-time Temperature Monitoring Dashboard

This Angular application provides real-time temperature monitoring with analytics for multiple laboratory environments.

## Features

### 🔥 Temperature Analytics Dashboard
- **5-Day Statistics**: Shows max, min, average, and current temperatures for the last 5 days
- **Real-time Updates**: Statistics automatically update as new WebSocket data arrives
- **Color-coded Temperatures**: Visual indicators for temperature ranges
- **Data Point Tracking**: Shows number of readings and timestamps
- **Multi-lab Support**: Automatically creates analytics for each unique lab

### 📊 Real-time Charts
- **Dynamic Chart Creation**: Automatically creates separate charts for each lab
- **Live Data Streaming**: Charts update in real-time with new temperature data
- **Interactive Charts**: Powered by Chart.js with zoom and hover capabilities
- **Data Management**: Maintains rolling window of recent data points

## Dashboard Components

### Analytics Cards
Each lab gets its own analytics card showing:
- **Current Temperature**: Most recent reading with time since update
- **Maximum Temperature**: Highest reading in last 5 days with timestamp
- **Minimum Temperature**: Lowest reading in last 5 days with timestamp
- **Average Temperature**: Mean temperature over last 5 days
- **Data Points Count**: Number of readings in the analytics window

### Temperature Color Coding
- 🔴 **Very Hot** (≥30°C): Red
- 🟠 **Hot** (25-29.9°C): Orange
- 🟢 **Normal** (20-24.9°C): Green
- 🔵 **Cool** (15-19.9°C): Blue
- 🟣 **Cold** (<15°C): Purple

## WebSocket Data Format

The application expects WebSocket data in this format:
```javascript
[
  {
    DateTime: "2025-06-23 08:38:56.206019",
    LabName: "Lab1",
    Temperature: 24.25
  },
  {
    DateTime: "2025-06-23 08:38:57.819215",
    LabName: "Lab2",
    Temperature: 26.31
  }
]
```

## Setup Instructions

### 1. Install Dependencies
```bash
npm install
```

### 2. Start the Angular Application
```bash
ng serve --port 4201
```

### 3. Start the Test WebSocket Server (Optional)
```bash
# Install WebSocket server dependencies
npm install ws

# Start the test server
node websocket-server.js
```

### 4. Access the Application
Open your browser to: `http://localhost:4201`
