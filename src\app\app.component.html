<div class="app-container">
  <h1 class="app-title">Real-time Temperature Monitoring</h1>

  <div class="connection-status">
    <span [class]="'status-indicator ' + (isConnected ? 'connected' : 'disconnected')"></span>
    <span class="status-text">{{ isConnected ? 'Connected' : 'Disconnected' }}</span>
  </div>

  <!-- Temperature Analytics Dashboard -->
  <div class="analytics-dashboard">
    <h2 class="dashboard-title">Temperature Analytics (Last 5 Days)</h2>

    <div class="analytics-grid">
      <div *ngFor="let stats of getAllLabStatistics()" class="analytics-card">
        <div class="card-header">
          <h3 class="lab-name">{{ stats.labName }}</h3>
          <span class="data-points-badge">{{ stats.dataPointsCount }} readings</span>
        </div>

        <div *ngIf="stats.hasRecentData; else noDataTemplate" class="stats-content">
          <!-- Current Temperature -->
          <div class="stat-item current-temp">
            <div class="stat-label">Current</div>
            <div [class]="'stat-value ' + getTemperatureColorClass(stats.currentTemp)">
              {{ formatTemperature(stats.currentTemp) }}
            </div>
            <div class="stat-time">{{ getTimeSinceUpdate(stats.currentTempTime) }}</div>
          </div>

          <!-- Maximum Temperature -->
          <div class="stat-item max-temp">
            <div class="stat-label">Max (5 days)</div>
            <div [class]="'stat-value ' + getTemperatureColorClass(stats.maxTemp)">
              {{ formatTemperature(stats.maxTemp) }}
            </div>
            <div class="stat-time">{{ stats.maxTempTime | date:'MMM d, HH:mm' }}</div>
          </div>

          <!-- Minimum Temperature -->
          <div class="stat-item min-temp">
            <div class="stat-label">Min (5 days)</div>
            <div [class]="'stat-value ' + getTemperatureColorClass(stats.minTemp)">
              {{ formatTemperature(stats.minTemp) }}
            </div>
            <div class="stat-time">{{ stats.minTempTime | date:'MMM d, HH:mm' }}</div>
          </div>

          <!-- Average Temperature -->
          <div class="stat-item avg-temp">
            <div class="stat-label">Average (5 days)</div>
            <div [class]="'stat-value ' + getTemperatureColorClass(stats.avgTemp)">
              {{ formatTemperature(stats.avgTemp) }}
            </div>
            <div class="stat-time">Based on {{ stats.dataPointsCount }} readings</div>
          </div>
        </div>

        <ng-template #noDataTemplate>
          <div class="no-recent-data">
            <p>No data available for the last 5 days</p>
          </div>
        </ng-template>
      </div>
    </div>
  </div>

  <div class="charts-container">
    <div *ngFor="let labName of getLabNames()" class="chart-wrapper">
      <h3 class="chart-title">{{ labName }} Temperature</h3>
      <div class="chart-info">
        <span class="data-count">Data points: {{ getDataCount(labName) }}</span>
        <span class="last-update">Last update: {{ getLastUpdate(labName) | date:'medium' }}</span>
      </div>
      <div class="chart-container">
        <canvas
          [id]="'chart-' + labName">
        </canvas>
      </div>
    </div>
  </div>

  <div *ngIf="getLabNames().length === 0" class="no-data">
    <p>Waiting for temperature data...</p>
  </div>
</div>