<div class="app-container">
  <h1 class="app-title">Real-time Temperature Monitoring</h1>

  <div class="connection-status">
    <span [class]="'status-indicator ' + (isConnected ? 'connected' : 'disconnected')"></span>
    <span class="status-text">{{ isConnected ? 'Connected' : 'Disconnected' }}</span>
  </div>

  <div class="charts-container">
    <div *ngFor="let labName of getLabNames()" class="chart-wrapper">
      <h3 class="chart-title">{{ labName }} Temperature</h3>
      <div class="chart-info">
        <span class="data-count">Data points: {{ getDataCount(labName) }}</span>
        <span class="last-update">Last update: {{ getLastUpdate(labName) | date:'medium' }}</span>
      </div>
      <div class="chart-container">
        <canvas
          [id]="'chart-' + labName"
          width="400"
          height="200">
        </canvas>
      </div>
    </div>
  </div>

  <div *ngIf="getLabNames().length === 0" class="no-data">
    <p>Waiting for temperature data...</p>
  </div>
</div>