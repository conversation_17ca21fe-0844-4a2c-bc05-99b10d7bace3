const WebSocket = require('ws');

// Create WebSocket server
const wss = new WebSocket.Server({ port: 8765 });

console.log('WebSocket server started on port 8765');

// Sample lab names
const labNames = ['Lab1', 'Lab2', 'Lab3'];

// Function to generate random temperature data
function generateTemperatureData() {
  const data = [];
  const now = new Date();
  
  // Generate 1-3 data points for random labs
  const numPoints = Math.floor(Math.random() * 3) + 1;
  
  for (let i = 0; i < numPoints; i++) {
    const labName = labNames[Math.floor(Math.random() * labNames.length)];
    const baseTemp = 20 + Math.random() * 10; // Base temperature between 20-30°C
    const variation = (Math.random() - 0.5) * 2; // ±1°C variation
    const temperature = parseFloat((baseTemp + variation).toFixed(2));
    
    // Add some milliseconds to make timestamps unique
    const timestamp = new Date(now.getTime() + i * 100);
    
    data.push({
      DateTime: timestamp.toISOString().replace('T', ' ').substring(0, 23),
      LabName: labName,
      Temperature: temperature
    });
  }
  
  return data;
}

// Handle client connections
wss.on('connection', function connection(ws) {
  console.log('Client connected');
  
  // Send initial data immediately
  ws.send(JSON.stringify(generateTemperatureData()));
  
  // Send data every 2 seconds
  const interval = setInterval(() => {
    if (ws.readyState === WebSocket.OPEN) {
      const data = generateTemperatureData();
      console.log('Sending data:', data);
      ws.send(JSON.stringify(data));
    }
  }, 2000);
  
  // Handle client disconnect
  ws.on('close', function() {
    console.log('Client disconnected');
    clearInterval(interval);
  });
  
  ws.on('error', function(error) {
    console.error('WebSocket error:', error);
    clearInterval(interval);
  });
});

// Handle server errors
wss.on('error', function(error) {
  console.error('WebSocket server error:', error);
});

console.log('WebSocket server is running. Connect to ws://localhost:8765');
console.log('Press Ctrl+C to stop the server');
